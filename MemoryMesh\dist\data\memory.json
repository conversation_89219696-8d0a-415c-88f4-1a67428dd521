{"type":"node","name":"workflow_wf_1754109342749_d0v4vxb8j","nodeType":"workflow","metadata":["workflow_id: wf_1754109342749_d0v4vxb8j","template_id: novel_standard_v1","template_version: 1.0.0","category: novel","status: not_started","current_stage: 0","total_stages: 4","progress: 0","created_at: 2025-08-02T04:35:42.749Z","updated_at: 2025-08-02T04:35:42.750Z","stages: [{\"id\":\"planning\",\"name\":\"規劃階段\",\"order\":0,\"status\":\"active\"},{\"id\":\"outline\",\"name\":\"大綱階段\",\"order\":1,\"status\":\"pending\"},{\"id\":\"chapter\",\"name\":\"章節階段\",\"order\":2,\"status\":\"pending\"},{\"id\":\"generation\",\"name\":\"生成階段\",\"order\":3,\"status\":\"pending\"}]"]}
{"type":"node","name":"stage_wf_1754109342749_d0v4vxb8j_planning","nodeType":"stage","metadata":["workflow_id: wf_1754109342749_d0v4vxb8j","stage_id: planning","stage_name: 規劃階段","stage_order: 0","status: active","required_node_types: [\"character\",\"setting\",\"theme\"]","optional_node_types: [\"timeline\",\"worldbuilding\"]","completion_criteria: {\"minNodes\":3,\"requiredFields\":[\"name\",\"description\",\"background\"],\"minimumQualityScore\":70,\"qualityChecks\":[{\"type\":\"character_depth\",\"description\":\"角色需要有明確的動機、衝突和成長弧線\",\"validator\":\"character_quality_validator\",\"weight\":0.4,\"required\":true},{\"type\":\"setting_richness\",\"description\":\"設定需要包含感官細節和文化背景\",\"validator\":\"setting_quality_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"theme_clarity\",\"description\":\"主題應該具體且能通過故事元素體現\",\"validator\":\"theme_quality_validator\",\"weight\":0.3,\"required\":true}],\"contentGuidelines\":{\"character\":{\"description\":\"創建具有三維特質的角色：外在目標、內在需求、致命缺陷\",\"examples\":[\"主角：年輕騎士，外在目標是拯救王國，內在需求是證明自己的價值，致命缺陷是過度自信\",\"反派：墮落法師，外在目標是獲得永生，內在需求是被理解和接納，致命缺陷是無法信任他人\"],\"qualityTips\":[\"每個角色都應該有獨特的說話方式和行為模式\",\"角色的背景故事要與當前情節相關\",\"確保角色有成長和變化的空間\"],\"commonMistakes\":[\"創建完美無缺的角色\",\"角色動機不明確或不合理\",\"所有角色說話方式相同\"]},\"setting\":{\"description\":\"描述不僅是地點，更是氛圍和情感的載體\",\"examples\":[\"古老的圖書館：塵埃在陽光中飛舞，羊皮紙的味道混合著蠟燭的香氣，暗示著知識的神秘和時間的流逝\",\"戰場：泥濘的土地，鐵鏽和血腥的味道，遠處的號角聲，體現戰爭的殘酷和英雄主義的複雜性\"],\"qualityTips\":[\"使用五感描述來增強沉浸感\",\"讓環境反映角色的內心狀態\",\"考慮設定對情節發展的影響\"],\"commonMistakes\":[\"過度描述無關緊要的細節\",\"設定與故事氛圍不符\",\"忽略環境對角色行為的影響\"]},\"theme\":{\"description\":\"主題應該通過角色行動和情節發展自然呈現\",\"examples\":[\"成長主題：通過主角面對挑戰、犯錯、學習的過程來體現\",\"友誼主題：通過角色間的互助、衝突、和解來展現\"],\"qualityTips\":[\"避免直接說教，讓主題通過故事自然流露\",\"確保主題與角色弧線和情節發展一致\",\"可以有多個相關的次主題\"],\"commonMistakes\":[\"主題過於抽象或模糊\",\"強行插入主題相關的對話\",\"主題與故事內容脫節\"]}}}","estimated_duration: 1-2 weeks","created_at: 2025-08-02T04:35:42.750Z"]}
{"type":"node","name":"stage_wf_1754109342749_d0v4vxb8j_outline","nodeType":"stage","metadata":["workflow_id: wf_1754109342749_d0v4vxb8j","stage_id: outline","stage_name: 大綱階段","stage_order: 1","status: pending","required_node_types: [\"plotarc\",\"timeline\"]","optional_node_types: [\"conflict\",\"theme\"]","completion_criteria: {\"minNodes\":1,\"requiredFields\":[\"main_plot\",\"key_events\"],\"minimumQualityScore\":75,\"qualityChecks\":[{\"type\":\"plot_structure\",\"description\":\"情節結構需要有清晰的起承轉合\",\"validator\":\"plot_structure_validator\",\"weight\":0.5,\"required\":true},{\"type\":\"pacing_balance\",\"description\":\"節奏安排需要有張弛有度的變化\",\"validator\":\"pacing_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"conflict_escalation\",\"description\":\"衝突需要有合理的升級和解決\",\"validator\":\"conflict_validator\",\"weight\":0.2,\"required\":false}],\"contentGuidelines\":{\"plotarc\":{\"description\":\"建立引人入勝的情節弧線，包含起因、發展、高潮、結局\",\"examples\":[\"三幕結構：建立(25%) → 對抗(50%) → 解決(25%)\",\"英雄之旅：平凡世界 → 冒險召喚 → 拒絕召喚 → 遇見導師 → 跨越門檻...\"],\"qualityTips\":[\"確保每個情節點都推動故事前進\",\"高潮應該是情感和行動的雙重頂點\",\"結局要回應開頭提出的問題\"],\"commonMistakes\":[\"中段情節拖沓，缺乏推進力\",\"高潮來得太突然或太平淡\",\"結局過於匆忙或留下太多未解之謎\"]},\"timeline\":{\"description\":\"建立清晰的時間線，確保事件邏輯合理\",\"examples\":[\"線性時間線：按時間順序發展的故事\",\"非線性時間線：使用倒敘、插敘等技巧\"],\"qualityTips\":[\"重要事件之間要有合理的時間間隔\",\"考慮季節、天氣對故事氛圍的影響\",\"確保角色成長與時間推移相符\"],\"commonMistakes\":[\"時間跳躍過於頻繁或突兀\",\"忽略時間對角色和環境的影響\",\"前後時間設定不一致\"]}}}","estimated_duration: 1 week","created_at: 2025-08-02T04:35:42.750Z"]}
{"type":"node","name":"stage_wf_1754109342749_d0v4vxb8j_chapter","nodeType":"stage","metadata":["workflow_id: wf_1754109342749_d0v4vxb8j","stage_id: chapter","stage_name: 章節階段","stage_order: 2","status: pending","required_node_types: [\"scene\",\"chapter\"]","optional_node_types: [\"dialogue\",\"description\"]","completion_criteria: {\"minNodes\":5,\"requiredFields\":[\"scene_setting\",\"characters_involved\"],\"minimumQualityScore\":80,\"qualityChecks\":[{\"type\":\"scene_depth\",\"description\":\"場景需要有豐富的感官細節和情感層次\",\"validator\":\"scene_depth_validator\",\"weight\":0.4,\"required\":true},{\"type\":\"dialogue_authenticity\",\"description\":\"對話需要符合角色性格且推動情節\",\"validator\":\"dialogue_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"pacing_control\",\"description\":\"章節節奏需要有適當的張弛變化\",\"validator\":\"chapter_pacing_validator\",\"weight\":0.3,\"required\":true}],\"contentGuidelines\":{\"scene\":{\"description\":\"創建生動的場景，平衡行動、對話、描述和內心獨白\",\"examples\":[\"行動場景：劍鬥 - 快節奏，短句，感官細節，情緒張力\",\"對話場景：談判 - 角色動機衝突，潛台詞，肢體語言\",\"反思場景：獨處 - 內心掙扎，回憶片段，情感深度\"],\"qualityTips\":[\"每個場景都要有明確的目的和衝突\",\"使用具體的感官細節增強沉浸感\",\"確保場景推動情節或角色發展\",\"注意場景間的過渡和連接\"],\"commonMistakes\":[\"場景過長或過短，節奏失衡\",\"缺乏具體的環境描述\",\"場景目的不明確，流於表面\",\"忽略角色在場景中的情感變化\"]},\"chapter\":{\"description\":\"構建完整的章節，有開頭鉤子、發展和結尾懸念\",\"examples\":[\"開頭鉤子：突發事件、謎團、衝突、有趣對話\",\"章節發展：推進主線、深化角色、揭示信息\",\"結尾懸念：未解問題、新的威脅、情感轉折\"],\"qualityTips\":[\"每章都要推進整體故事進程\",\"保持讀者的閱讀興趣和期待\",\"平衡不同類型的內容（行動、對話、描述）\",\"確保章節長度適中且一致\"],\"commonMistakes\":[\"章節缺乏內在結構和節奏\",\"結尾過於平淡，缺乏懸念\",\"章節間缺乏連貫性\",\"過度依賴對話或描述\"]}}}","estimated_duration: 2-3 weeks","created_at: 2025-08-02T04:35:42.750Z"]}
{"type":"node","name":"stage_wf_1754109342749_d0v4vxb8j_generation","nodeType":"stage","metadata":["workflow_id: wf_1754109342749_d0v4vxb8j","stage_id: generation","stage_name: 生成階段","stage_order: 3","status: pending","required_node_types: [\"chapter\"]","optional_node_types: [\"revision_notes\"]","completion_criteria: {\"minNodes\":1,\"requiredFields\":[\"content\",\"word_count\"],\"minimumQualityScore\":85,\"qualityChecks\":[{\"type\":\"narrative_consistency\",\"description\":\"敘述風格和視角需要保持一致\",\"validator\":\"narrative_consistency_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"character_voice\",\"description\":\"角色聲音需要獨特且一致\",\"validator\":\"character_voice_validator\",\"weight\":0.3,\"required\":true},{\"type\":\"thematic_integration\",\"description\":\"主題需要自然融入故事中\",\"validator\":\"theme_integration_validator\",\"weight\":0.2,\"required\":true},{\"type\":\"prose_quality\",\"description\":\"文筆需要流暢且富有表現力\",\"validator\":\"prose_quality_validator\",\"weight\":0.2,\"required\":false}],\"contentGuidelines\":{\"chapter\":{\"description\":\"完成高質量的章節內容，整合所有故事元素\",\"examples\":[\"完整章節：開頭引人入勝，中段發展充實，結尾留有懸念\",\"修訂版本：基於反饋改進的內容，提升可讀性和影響力\"],\"qualityTips\":[\"確保每章都有明確的情節進展\",\"保持敘述聲音的一致性\",\"平衡描述、對話、行動和內心獨白\",\"注意章節間的過渡和連貫性\",\"最終校對語法、拼寫和格式\"],\"commonMistakes\":[\"急於完成而忽略質量\",\"前後章節風格不一致\",\"忽略細節的打磨和完善\",\"缺乏最終的整體審視和修訂\"]}}}","estimated_duration: 4-6 weeks","created_at: 2025-08-02T04:35:42.750Z"]}
{"metadata":["projectName: 我，上帝指定監護人，被炒了","genre: ['都市奇幻', '科幻', '歷史奇幻', '喜劇']","targetAudience: 喜歡都市奇幻、歷史陰謀論與輕鬆幽默風格的網路小說讀者。","writingStyle: 以輕鬆幽默的吐槽風格為主，穿插對歷史與社會的犀利點評。對話風趣，節奏明快。","plannedLength: 長篇小說，預計超過1000章。","currentProgress: 第一階段：故事核心與大綱確立。","worldBuilding: 一個魔法、神話與科技並存的現代世界。存在著被稱為「世界維護者」的長壽種族，他們從幕後引導人類文明，分為不同世代。歷史名人皆為長壽種族的一員。","plotStructure: 開篇以主角競選總統被暗殺的事件作為引子，製造懸念與反差。核心劇情圍繞主角經營的「萬事通顧問公司」展開，以單元劇形式解決現代人的奇葩委託，並在過程中逐步揭示暗殺事件的真相。主線將穿插與其他「世界維護者」的互動，以及偶爾處理如擊退外星人、與滅霸喝茶等宇宙級事件。","thematicElements: 權力與責任、理想與現實、歷史的荒誕性、傳統與現代的衝突與融合。","characterSettings: 主角為第四代巫妖維護者，性格傲嬌但內心有正義感。夥伴包括沉迷科技的精靈愛因斯坦和頑固的地精奧本海默等。他們之間充滿理念衝突和有趣的日常互動。","notes: 故事基調為黑色幽默，充滿反差感（如莊嚴復活後怒吃大麥克）。避免傳統的熱血戰鬥，更側重於理念的碰撞和充滿機鋒的對白。"],"nodeType":"NovelProject","name":"我，上帝指定監護人，被炒了 - 項目設定","type":"node"}
{"type":"node","name":"維克多 (賽巴斯汀)","nodeType":"character","metadata":["Name: 維克多 (賽巴斯汀)","Role: 主角 (位面維護者)","Status: Active","Description: 第四代位面維護者(巫妖)，當代化名「維克多」。他擁有一個極其冗長的古老真名，以「賽巴斯汀」結尾，只有老友會如此稱呼。他經營著「萬事通顧問公司」，同時也是自己很久以前覺得好玩而創立的某個新興宗教的「神明」。其公司員工是他從古至今賦予巫妖生命並跟隨他的下屬。他的日常極其繁忙：處理凡人委託、與其他位面的維護者(如孔子)打電話發牢騷、回應信徒的祈禱，偶爾還要處理因真名外流而被異世界召喚實現願望的突發事件。","Background: 全名為「亞歷山德·馮·埃德里奇·馬格努斯·卡斯比安·賽巴斯汀」。作為第四代維護者，他與同代人（如愛因斯坦、奧本海默）共同接手管理多個位面。\n【核心秘密，待揭示】在19世紀末至20世紀初，他曾以「夏洛克·福爾摩斯」的化名，在倫敦活躍。他那些被譽為「神技」的演繹法，實則是對「靈魂殘響」、「時間印記」等超自然資訊的感知與解讀。他當年的摯友華生醫生，是一位完全不知情的普通人類，而他最大的對手莫里亞蒂，則是他遇到的第一個與「混沌福音」有著深刻聯繫的人間代理人。這段經歷，是他漫長生命中，對「解謎」這項事業最為投入的一段高光時期。","Traits: 傲嬌, 吐槽役, 黑色幽默, 內心有正義感, 知識淵博, 雙重身分, 神祇(自封), 位面管理者, 被召喚者","Abilities: 永生(命匣), 位面通訊, 跨位面召喚響應, 賦予巫妖生命","Importance: Protagonist","Internalconflict: 在「引導文明」的宏大責任與「處理信徒奇葩祈禱」、「被異世界強行召喚」的繁瑣日常之間掙扎。","Currentlocation: 萬事通顧問公司"]}
{"type":"node","name":"萬事通顧問公司","nodeType":"setting","metadata":["Name: 萬事通顧問公司","Type: Building","Description: 主角維克多在現代社會經營的復古風格建築。\n內部結構：\n【已確認】一樓：對外營業的辦公區，裝潢古典雅緻。\n【已確認】地下室：不為人知的核心密室，一半煉金術一半高科技風格，內有石棺。\n【預設，可修改】二樓：維克多的私人起居空間。\n【預設，可修改】三樓：巴納比的資料庫與工作室。\n顯著特徵：\n【已確認】建築物隔壁，是一家由伊莎貝拉打理的、品味極佳的花店。\n【預設，可修改】公司內部可能有一個隱藏的VIP服務區，以及一個能連接到異空間的儲藏室。","Status: Active","Significance: Critical","Notablefeatures: 復古裝潢, 隱藏的VIP服務區, 隔壁花店","Relatedcharacters: 維克多 (賽巴斯汀), 伊莎貝拉, 巴納比, 健司","Sublocations: 一樓辦公區, 地下室密室, 二樓起居室, 三樓圖書室"]}
{"type":"node","name":"伊莎貝拉","nodeType":"character","metadata":["Name: 伊莎貝拉","Role: 公司總管 & 宗教大祭司","Status: Active","Description: 「萬事通顧問公司」的實際運營者和「契約精神聖殿」的大祭司。她精通政治與人際關係，負責將維克多的奇思妙想轉化為可行方案，並以莊重神聖的方式呈現荒謬的宗教儀式。","Background: 原為文藝復興時期的義大利女貴族，因政治陰謀瀕死時被維克多(賽巴斯汀)所救，成為他最信賴的副手和第一位巫妖下屬。","Traits: 優雅, 沉穩, 腹黑, 管理能力強, 諷刺幽默","Importance: Supporting Character","Motivation: 維持公司的正常運轉，並確保老闆的「偉大事業」不至於因其本人的脫線而徹底崩盤。","Currentlocation: 萬事通顧問公司"]}
{"type":"node","name":"巴納比","nodeType":"character","metadata":["Name: 巴納比","Role: 檔案管理員 & 聖殿書記官","Status: Active","Description: 公司的首席研究員和「契約精神聖殿」的書記官。他管理著連接多元宇宙的龐大資料庫，負責記錄所有「神諭」和「契約」，並對文字記錄有著近乎偏執的熱情。","Background: 原為中世紀的修道院手抄員，在修道院即將被戰火焚毀時，連人帶圖書館被維克多(賽巴斯汀)整體救下，成為其麾下第二位核心成員。","Traits: 內向, 博學, 強迫症, 不善交際, 細節控","Importance: Supporting Character","Motivation: 保護和整理所有知識，確保每一份記錄都絕對精確，並在浩如煙海的資料中尋找被遺忘的關聯性。","Currentlocation: 萬事通顧問公司"]}
{"type":"node","name":"健司","nodeType":"character","metadata":["name: 健司","role: 保全 & 聖殿守衛","status: Active","description: 公司的安全主管、外勤執行者和「契約精神聖殿」的唯一守衛。他負責處理所有物理層面的威脅，並以古老的武士道精神來理解和執行現代任務。","background: 原為日本戰國時代的一位忠誠武士，在護主戰死後，其靈魂的忠誠與勇氣打動了維克多(賽巴斯汀)，被賦予了新的身體和使命，成為團隊的第三位核心成員。","traits: 沉默寡言, 極度忠誠, 行動力強, 一本正經的幽默感, 文化錯位","importance: Supporting Character","motivation: 以生命守護主君（維克多）及其事業，並在現代社會中尋找踐行自己武士道的方式。","currentLocation: "]}
{"type":"node","name":"位面維護者","nodeType":"organization","metadata":["name: 位面維護者","type: Secret Society","description: 一個鬆散、混亂且橫跨多元宇宙的長壽種監護組織。其管理模式極其隨機，某個位面的負責人通常由幾位維護者在「位面通訊」中臨時討論決定，可共治也可獨攬。現任地球負責人維克多，就面臨著同代夥伴（愛因斯坦、奧本海默）集體躺平，而第一代創始人（雅典娜）也毫無干涉意願的局面，給予了他極大的自主權。","status: Active","goals: 引導文明, 防止位面毀滅, 維護宇宙平衡, 偶爾互相甩鍋","internalCulture: 成員間的互動充滿了跨宇宙的抱怨、甩鍋、理念衝突和偶爾的合作。他們極力避免物理戰爭，更傾向於用影響力、佈局和無休止的爭論來解決問題。一個常見的招聘模式是：在通訊中誰提出了好想法，誰就可能被抓去負責執行。"]}
{"nodeType":"MaintainerGeneration","name":"第一代維護者 (神話時代)","metadata":[],"type":"node"}
{"metadata":[],"name":"第二代維護者 (古典時代)","nodeType":"MaintainerGeneration","type":"node"}
{"nodeType":"MaintainerGeneration","metadata":[],"name":"第三代維護者 (文藝復興至啟蒙時代)","type":"node"}
{"nodeType":"MaintainerGeneration","metadata":[],"name":"第四代維護者 (工業革命至今)","type":"node"}
{"type":"node","name":"雅典娜","nodeType":"character","metadata":["name: 雅典娜","role: 第一代維護者","status: Inactive","description: 智慧與戰略的化身，地球的創始維護者之一，目前對地球事務持放任態度。","background: 神話時代的地球維護者，主角維克多的名義上的老師。","traits: 智慧, 超然, 放任主義","importance: Supporting Character","currentLocation: "]}
{"type":"node","name":"奧丁","nodeType":"character","metadata":["name: 奧丁","role: 第一代維護者","status: Active","description: 為了知識可以犧牲一切的北歐主神，熱衷於收集各個位面的秘密，是巫妖的另一代表。","background: 北歐神話中的主神。","traits: 求知慾強, 老謀深算, 信息販子","importance: Minor Character","currentLocation: "]}
{"type":"node","name":"阿努比斯","nodeType":"character","metadata":["name: 阿努比斯","role: 第一代維護者","status: Active","description: 亡者與契約的守護者，對規則和靈魂平衡極度敏感，是巫妖族的代表。","background: 埃及神話中的死神。","traits: 守序, 嚴肅, 契約精神","importance: Minor Character","currentLocation: "]}
{"type":"node","name":"女媧","nodeType":"character","metadata":["name: 女媧","role: 第一代維護者","status: Active","description: 東方的創世者，擅長生命創造與修補位面漏洞，是龍族的代表。","background: 中國神話中的創世女神。","traits: 慈愛, 創造者, 修補匠","importance: Minor Character","currentLocation: "]}
{"type":"node","name":"亞瑟王","nodeType":"character","metadata":["name: 亞瑟王","role: 第二代維護者","status: Active","description: 理想主義的王者，試圖建立完美的秩序，但往往因過於理想而失敗。龍族代表。","background: 不列顛傳說中的國王。","traits: 理想主義, 王者風範, 騎士精神","importance: Minor Character","currentLocation: "]}
{"type":"node","name":"克麗奧佩脫拉","nodeType":"character","metadata":["name: 克麗奧佩脫拉","role: 第二代維護者","status: Active","description: 魅力與政治手腕的巔峰，能輕易地讓位面內的英雄豪傑為她服務。龍族代表。","background: 古埃及托勒密王朝的末代女王。","traits: 魅力非凡, 政治手腕高超, 擅長交際","importance: Minor Character","currentLocation: "]}
{"type":"node","name":"阿基米德","nodeType":"character","metadata":["name: 阿基米德","role: 第二代維護者","status: Active","description: 天才發明家，能用一個槓桿撬動星球，前提是先給他一個支點。地精代表。","background: 古希臘的數學家、物理學家、工程師、發明家和天文學家。","traits: 天才, 發明家, 務實","importance: Minor Character","currentLocation: "]}
{"type":"node","name":"孫子","nodeType":"character","metadata":["name: 孫子","role: 第二代維護者","status: Active","description: 兵法大師，認為所有衝突都可以在發生前通過計算和佈局來解決。精靈代表。","background: 中國古代的軍事家。","traits: 戰略家, 計算精確, 避免衝突","importance: Minor Character","currentLocation: "]}
{"type":"node","name":"達文西","nodeType":"character","metadata":["name: 達文西","role: 第三代維護者","status: Active","description: 全才，既有地精的工程學頭腦，又有精靈的藝術感。是維護者中的多面手。","background: 文藝復興時期的博學者。","traits: 全才, 藝術家, 發明家","importance: Minor Character","currentLocation: "]}
{"type":"node","name":"伽利略","nodeType":"character","metadata":["name: 伽利略","role: 第三代維護者","status: Active","description: 頑固的科學家，因為堅持自己的觀測結果，沒少和其他維護者吵架。地精代表。","background: 義大利物理學家、數學家、天文學家和哲學家。","traits: 科學家, 頑固, 實證主義","importance: Minor Character","currentLocation: "]}
{"type":"node","name":"莎士比亞","nodeType":"character","metadata":["name: 莎士比亞","role: 第三代維護者","status: Active","description: 人類情感的觀察家與編劇，認為整個世界就是一個巨大的舞台。精靈代表。","background: 英國劇作家、詩人和演員。","traits: 劇作家, 洞察人性, 浪漫主義","importance: Minor Character","currentLocation: "]}
{"type":"node","name":"伊莉莎白一世","nodeType":"character","metadata":["name: 伊莉莎白一世","role: 第三代維護者","status: Active","description: 極具統治力的女王，將自己的位面打理得井井有條。龍族代表。","background: 英格蘭和愛爾蘭女王。","traits: 女王, 統治力, 精明","importance: Minor Character","currentLocation: "]}
{"type":"node","name":"愛因斯坦","nodeType":"character","metadata":["name: 愛因斯坦","role: 第四代維護者","status: Active","description: 相對論的提出者，沉迷AR遊戲，認為一切紛爭在宇宙尺度下都無關緊要。精靈代表。","background: 德裔美國物理學家。","traits: 理論物理學家, 遊戲宅, 和平主義者","importance: Supporting Character","currentLocation: "]}
{"type":"node","name":"瑪麗·居禮","nodeType":"character","metadata":["name: 瑪麗·居禮","role: 第四代維護者","status: Active","description: 放射性元素的研究者，對探索未知而危險的力量有著純粹的熱情。巫妖代表。","background: 波蘭和法國的物理學家和化學家，放射性研究的先驅。","traits: 科學家, 專注, 無畏","importance: Minor Character","currentLocation: "]}
{"type":"node","name":"奧本海默","nodeType":"character","metadata":["name: 奧本海默","role: 第四代維護者","status: Active","description: 核能的釋放者，一個為了「最優解」而打開潘朵拉魔盒的工程師。地精代表。","background: 美國理論物理學家，被譽為「原子彈之父」。","traits: 工程師, 實用主義, 結果導向","importance: Supporting Character","currentLocation: "]}
{"type":"node","name":"普羅米修斯","nodeType":"character","metadata":["name: 普羅米修斯","role: 第一代維護者","status: Active","description: 盜火者，對人類技術的發展有著偏執的愛好，是地精族的代表人物。","background: 泰坦神，因盜火給人類而聞名。","traits: 技術宅, 親人類, 理想主義","importance: Minor Character","currentLocation: "]}
{"type":"node","name":"秦始皇","nodeType":"character","metadata":["name: 秦始皇","role: 第二代維護者","status: Active","description: 強調絕對權力與統一的鐵腕帝王，與亞瑟王的理念完全相反。龍族代表。","background: 中國第一位皇帝。","traits: 鐵腕, 權力慾, 統一思想","importance: Minor Character","currentLocation: "]}
{"type":"node","name":"牛頓","nodeType":"character","metadata":["name: 牛頓","role: 第三代維護者","status: Active","description: 沉迷於宇宙法則，晚年試圖用煉金術解釋萬有引力。巫妖代表。","background: 英國物理學家、數學家、天文學家、自然哲學家和煉金術士。","traits: 科學家, 神秘主義, 煉金術士","importance: Minor Character","currentLocation: "]}
{"type":"node","name":"特斯拉","nodeType":"character","metadata":["name: 特斯拉","role: 第四代維護者","status: Active","description: 超前時代的發明家，腦中充滿了各種瘋狂的能量傳輸計畫。地精代表。","background: 塞爾維亞裔美國發明家、物理學家、機械工程師、電氣工程師和未來學家。","traits: 發明家, 未來主義者, 理想主義","importance: Minor Character","currentLocation: "]}
{"type":"node","name":"契約精神聖殿","nodeType":"organization","metadata":["name: 契約精神聖殿","type: Religious Organization","description: 主角維克多覺得好玩而創立的虛構宗教。核心教義是「等價交換，明碼標價」。然而，由於教義在跨位面傳播中的誤解和扭曲，經常在其他世界引發各種荒誕的、甚至被誤認為是「邪教」的儀式，成為主角一個持續的麻煩來源。","status: Active"]}
{"type":"node","name":"第四代維護者私下群組","nodeType":"organization","metadata":["name: 第四代維護者私下群組","type: Social Club","description: 一個存在於「位面通訊系統」中的加密聊天群組，成員僅限第四代維護者。這裡是他們抱怨、吐槽、甩鍋和偶爾正經討論工作的地方。群組名可能就叫「這屆人類真難帶」。","status: Active"]}
{"type":"node","name":"主角的命匣","nodeType":"symbolic_object","metadata":["name: 主角的命匣","description: 維克多作為巫妖的生命核心。它可能不是傳統的珠寶盒，而是一個出人意料的現代物品，例如一個從未停產過的諾基亞3310手機、一個伺服器硬碟，或是一個被遺忘在月球上的高爾夫球。它的安危直接關係到維克多的存亡。","type: Personal Item","significance: Critical Plot Device","symbolicMeaning: 永生與詛咒, 脆弱的生命核心","narrativeFunction: 主角的核心弱點, 主線劇情的關鍵道具"]}
{"type":"node","name":"混沌福音","nodeType":"organization","metadata":["name: 混沌福音","type: Secret Society","description: 一種思想病毒，源自一位瘋狂的初代維護者。它主張真正的進化源於徹底的崩潰與重組，通過腐化思想和引誘文明的方式，將世界從「維護者」的枷鎖中解放出來。","status: Active"]}
{"type":"node","name":"衰變之熵","nodeType":"organization","metadata":["name: 衰變之熵","type: Secret Society","description: 所有長壽種內心深處最終都會面臨的終極絕望。它是一種精神上的「熵」，一種認為「一切終將毫無意義」的、不可逆轉的衰變過程。它會放大維護者們的倦怠感，切斷他們行動的意義，最終誘發他們徹底「放手」。","status: Active"]}
{"metadata":["核心目標: 應對超長篇小說的長度與方向問題。","執行方法1: 以「卷」為單位推進，每卷(100-200章)都有獨立的起承轉合和核心目標。","執行方法2: 只在當前卷開始前詳細規劃細綱，保持創作靈活性。","執行方法3: 以「衰變之熵」和「混沌福音」為兩大燈塔，確保所有劇情在主題上與主線保持關聯，防止跑偏。"],"nodeType":"CreativeStrategy","name":"創作策略：滾動式大綱與燈塔式創作","type":"node"}
{"metadata":["核心目標: 高效填充日常劇情，同時穩定推進主線。","執行方法1: 設計可複用的單元劇模塊（如奇葩委託、同行甩鍋、邪教爛攤子）。","執行方法2: 規定每3-5個單元劇中，必須至少有1個包含關於主線的「鉤子」（新線索或危機加重）。","執行方法3: 使用MemoryMesh的Event和StoryThread節點，系統化追蹤所有伏筆，確保回收。"],"name":"創作策略：模塊化單元劇與主線鉤子","nodeType":"CreativeStrategy","type":"node"}
{"metadata":["核心目標: 防止故事中期乏力，確保情感深度。","執行方法1: 為重要配角設計與「衰變之熵」相關的個人危機，創造內在矛盾。","執行方法2: 充分利用「神仙同事」間的理念衝突和關係變化來製造劇情。","執行方法3: 定期讓劇情回歸「萬事通顧問公司」這個「家」，作為故事的情感錨點。"],"nodeType":"CreativeStrategy","name":"創作策略：角色驅動的內在動力","type":"node"}
{"type":"node","name":"序章：總統候選人暗殺事件","nodeType":"event","metadata":["name: 序章：總統候選人暗殺事件","type: Plot Event","description: 主角維克多在以總統候選人身份進行演說的直播中，被狙擊槍當眾暗殺。這個事件是引導讀者誤以為是政治驚悚小說的關鍵佈局，並為後續主角在命匣中復活、怒吃大麥克的反差劇情埋下伏筆。","importance: Major Turning Point","consequences: 主角從命匣復活, 引發公眾對暗殺事件的巨大討論, 主角的朋友打電話來嘲笑","narrativePurpose: 製造懸念與強烈反差，快速建立主角的獨特處境，並引出故事的核心世界觀。","participants: 維克多 (賽巴斯汀)"]}
{"metadata":["核心原則: 利用刻板印象，打破刻板印象。","執行方法1: 利用讀者對歷史人物的既有認知，降低理解門檻。","執行方法2: 通過展現角色與其公眾認知截然相反的一面（如愛因斯坦是遊戲宅），來製造驚喜、塑造個性、創造趣味。"],"nodeType":"CreativeStrategy","name":"角色塑造核心理念：反差感原則","type":"node"}
{"type":"node","name":"一樓辦公區","nodeType":"setting","metadata":["name: 一樓辦公區","type: Room","description: 萬事通顧問公司對外營業的場所，裝潢古典雅緻，是接待客戶和團隊日常工作的空間。","status: Active"]}
{"type":"node","name":"三樓圖書室","nodeType":"setting","metadata":["name: 三樓圖書室","type: Room","description: 巴納比的個人領域，堆滿了伺服器、古籍和各種分析儀器。【預設，可修改】","status: Active"]}
{"type":"node","name":"二樓起居室","nodeType":"setting","metadata":["name: 二樓起居室","type: Room","description: 維克多的私人起居空間。【預設，可修改】","status: Active"]}
{"type":"node","name":"地下室密室","nodeType":"setting","metadata":["name: 地下室密室","type: Room","description: 不為人知的核心密室，一半是古典煉金術風格，一半是未來科技風格，中央擺放著維克多的復活石棺。","status: Active"]}
{"type":"node","name":"單元劇：畫廊裡的陌生人","nodeType":"event","metadata":["name: 單元劇：畫廊裡的陌生人","type: Plot Event","description: 【核心衝突】一位功成名就的畫家，在享受著“神賜靈感”帶來的名利的同時，也被其副作用折磨得瀕臨崩潰。維克多團隊通過調查，揭示了其與“惡魔低語”交易的真相，並最終幫助他從“維持成功”與“回歸自我”的艱難抉擇中獲得解脫的故事。","importance: Plot Advancement","narrativePurpose: 【深層核心】探討在巨大的成功與壓力面前，一個人是否會為了維持成功，而選擇放棄“自我”與“真實”。【主線鉤子】從交易媒介（被詛咒的畫筆）中，發現與刺殺事件的“惡意”同源的線索。","status: In Progress"]}
{"type":"node","name":"朱利安·索倫蒂諾","nodeType":"character","metadata":["name: 朱利安·索倫蒂諾","role: 本單元劇核心人物（畫家）","status: Introduced","description: 一位頗有成就的畫家，其風格詭異而充滿力量。他深陷在「維持成功」的喜悅和「失去自我」的恐懼中，是本次委託的核心調查對象。","currentLocation: "]}
{"type":"node","name":"海倫娜·索倫蒂諾","nodeType":"character","metadata":["name: 海倫娜·索倫蒂諾","role: 本單元劇委託人（畫家之妻）","status: Introduced","description: 她不理解丈夫畫中的“偉大”，只懷念過去那份雖然稚嫩但充滿溫暖的情感。她以“找回丈夫”為目的，向萬事通顧問公司發出委託。","currentLocation: "]}
{"from":"stage_wf_1754109342749_d0v4vxb8j_planning","to":"stage_wf_1754109342749_d0v4vxb8j_outline","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"stage_wf_1754109342749_d0v4vxb8j_outline","to":"stage_wf_1754109342749_d0v4vxb8j_chapter","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"stage_wf_1754109342749_d0v4vxb8j_chapter","to":"stage_wf_1754109342749_d0v4vxb8j_generation","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1754109342749_d0v4vxb8j","to":"stage_wf_1754109342749_d0v4vxb8j_planning","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1754109342749_d0v4vxb8j","to":"stage_wf_1754109342749_d0v4vxb8j_outline","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1754109342749_d0v4vxb8j","to":"stage_wf_1754109342749_d0v4vxb8j_chapter","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1754109342749_d0v4vxb8j","to":"stage_wf_1754109342749_d0v4vxb8j_generation","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"stage_wf_1754109342749_d0v4vxb8j_chapter","to":"stage_wf_1754109342749_d0v4vxb8j_generation","edgeType":"next_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1754109342749_d0v4vxb8j","to":"stage_wf_1754109342749_d0v4vxb8j_planning","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1754109342749_d0v4vxb8j","to":"stage_wf_1754109342749_d0v4vxb8j_outline","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1754109342749_d0v4vxb8j","to":"stage_wf_1754109342749_d0v4vxb8j_chapter","edgeType":"contains_stage","weight":1,"type":"edge"}
{"from":"workflow_wf_1754109342749_d0v4vxb8j","to":"stage_wf_1754109342749_d0v4vxb8j_generation","edgeType":"contains_stage","weight":1,"type":"edge"}
{"type":"edge","from":"維克多 (賽巴斯汀)","to":"萬事通顧問公司","edgeType":"located_in"}
{"type":"edge","from":"伊莎貝拉","to":"萬事通顧問公司","edgeType":"located_in"}
{"type":"edge","from":"伊莎貝拉","to":"萬事通顧問公司","edgeType":"located_in"}
{"type":"edge","from":"巴納比","to":"萬事通顧問公司","edgeType":"located_in"}
{"type":"edge","from":"萬事通顧問公司","to":"維克多 (賽巴斯汀)","edgeType":"present_in"}
{"type":"edge","from":"萬事通顧問公司","to":"伊莎貝拉","edgeType":"present_in"}
{"type":"edge","from":"萬事通顧問公司","to":"巴納比","edgeType":"present_in"}
{"type":"edge","from":"萬事通顧問公司","to":"健司","edgeType":"present_in"}
{"to":"位面維護者","from":"雅典娜","edgeType":"is_member_of","type":"edge"}
{"edgeType":"belongs_to_generation","from":"雅典娜","to":"第一代維護者 (神話時代)","type":"edge"}
{"to":"位面維護者","edgeType":"is_member_of","from":"阿努比斯","type":"edge"}
{"to":"第一代維護者 (神話時代)","from":"阿努比斯","edgeType":"belongs_to_generation","type":"edge"}
{"from":"普羅米修斯","to":"位面維護者","edgeType":"is_member_of","type":"edge"}
{"from":"普羅米修斯","to":"第一代維護者 (神話時代)","edgeType":"belongs_to_generation","type":"edge"}
{"to":"位面維護者","edgeType":"is_member_of","from":"亞瑟王","type":"edge"}
{"to":"第二代維護者 (古典時代)","from":"亞瑟王","edgeType":"belongs_to_generation","type":"edge"}
{"from":"克麗奧佩脫拉","edgeType":"is_member_of","to":"位面維護者","type":"edge"}
{"to":"第二代維護者 (古典時代)","from":"克麗奧佩脫拉","edgeType":"belongs_to_generation","type":"edge"}
{"edgeType":"is_member_of","from":"秦始皇","to":"位面維護者","type":"edge"}
{"edgeType":"belongs_to_generation","to":"第二代維護者 (古典時代)","from":"秦始皇","type":"edge"}
{"from":"達文西","edgeType":"is_member_of","to":"位面維護者","type":"edge"}
{"to":"第三代維護者 (文藝復興至啟蒙時代)","from":"達文西","edgeType":"belongs_to_generation","type":"edge"}
{"to":"位面維護者","edgeType":"is_member_of","from":"伊莉莎白一世","type":"edge"}
{"edgeType":"belongs_to_generation","from":"伊莉莎白一世","to":"第三代維護者 (文藝復興至啟蒙時代)","type":"edge"}
{"edgeType":"is_member_of","to":"位面維護者","from":"牛頓","type":"edge"}
{"to":"第三代維護者 (文藝復興至啟蒙時代)","from":"牛頓","edgeType":"belongs_to_generation","type":"edge"}
{"edgeType":"is_member_of","from":"維克多 (賽巴斯汀)","to":"位面維護者","type":"edge"}
{"from":"維克多 (賽巴斯汀)","edgeType":"belongs_to_generation","to":"第四代維護者 (工業革命至今)","type":"edge"}
{"from":"奧本海默","to":"位面維護者","edgeType":"is_member_of","type":"edge"}
{"edgeType":"belongs_to_generation","to":"第四代維護者 (工業革命至今)","from":"奧本海默","type":"edge"}
{"from":"特斯拉","edgeType":"is_member_of","to":"位面維護者","type":"edge"}
{"edgeType":"belongs_to_generation","from":"特斯拉","to":"第四代維護者 (工業革命至今)","type":"edge"}
{"type":"edge","from":"序章：總統候選人暗殺事件","to":"維克多 (賽巴斯汀)","edgeType":"participates_in"}
{"type":"edge","from":"萬事通顧問公司","to":"一樓辦公區","edgeType":"contains"}
{"type":"edge","from":"萬事通顧問公司","to":"地下室密室","edgeType":"contains"}
{"type":"edge","from":"萬事通顧問公司","to":"二樓起居室","edgeType":"contains"}
{"type":"edge","from":"萬事通顧問公司","to":"三樓圖書室","edgeType":"contains"}
{"from":"朱利安·索倫蒂諾","to":"單元劇：畫廊裡的陌生人","edgeType":"participates_in","type":"edge"}
{"to":"單元劇：畫廊裡的陌生人","edgeType":"participates_in","from":"海倫娜·索倫蒂諾","type":"edge"}
